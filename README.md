# Ecommerce Frontend - React + Fluent UI

Una aplicación frontend moderna para el sistema de ecommerce, construida con React, TypeScript, Fluent UI y Rsbuild.

## 🚀 Características

- ✅ **Autenticación completa** con JWT tokens
- 🎨 **Interfaz moderna** con Fluent UI components
- 🔒 **Manejo seguro de sesiones** y roles de usuario
- 📱 **Diseño responsivo** y accesible
- ⚡ **Build rápido** con Rsbuild (Rspack)
- 🔧 **TypeScript** para desarrollo type-safe

## 🛠️ Tecnologías

- **React 17** - Biblioteca de UI
- **TypeScript** - Tipado estático
- **Fluent UI 8** - Sistema de diseño de Microsoft
- **Rsbuild** - Build tool basado en Rspack
- **Biome** - Linter y formatter

## 📋 Prerequisitos

- Node.js 16+
- pnpm (recomendado) o npm

## ⚙️ Configuración

### 1. Instalar dependencias

```bash
pnpm install
```

### 2. Configurar variables de entorno

Copia el archivo de ejemplo y configura tu API:

```bash
cp .env.example .env
```

Edita `.env` con la URL de tu API:

```env
REACT_APP_API_URL=http://localhost:8080
```

### 3. Iniciar el servidor de desarrollo

```bash
pnpm dev
```

La aplicación estará disponible en `http://localhost:3000`

## 🏗️ Scripts Disponibles

```bash
# Desarrollo
pnpm dev              # Inicia el servidor de desarrollo

# Producción
pnpm build            # Construye la aplicación para producción
pnpm preview          # Previsualiza el build de producción

# Calidad de código
pnpm check            # Ejecuta Biome check y corrige automáticamente
pnpm format           # Formatea el código con Biome
```

## 🔐 Autenticación

La aplicación incluye un sistema completo de autenticación que consume la API de ecommerce:

### Funcionalidades
- **Login** con username/password
- **Registro** de nuevos usuarios con roles
- **Perfil de usuario** con información detallada
- **Logout** seguro
- **Manejo de roles** (Admin, Vendedor, Usuario)

### Credenciales de prueba
- **Username**: `admin`
- **Password**: `admin123`

Para más detalles, consulta [docs/AUTHENTICATION_IMPLEMENTATION.md](docs/AUTHENTICATION_IMPLEMENTATION.md)

## 📁 Estructura del Proyecto

```
src/
├── components/          # Componentes React
│   ├── AuthContainer.tsx
│   ├── LoginForm.tsx
│   ├── SignupForm.tsx
│   └── UserProfile.tsx
├── contexts/           # React Contexts
│   └── AuthContext.tsx
├── services/           # Servicios de API
│   └── authService.ts
├── types/              # Tipos TypeScript
│   └── auth.ts
├── config/             # Configuración
│   └── api.ts
└── App.tsx            # Componente principal
```

## 🔗 API Backend

Esta aplicación está diseñada para trabajar con la API de ecommerce. Asegúrate de que el backend esté ejecutándose en el puerto configurado.

### Endpoints utilizados:
- `POST /api/auth/login` - Iniciar sesión
- `POST /api/auth/signup` - Registrar usuario
- `GET /api/auth/session-info` - Información de sesión
- `POST /api/auth/logout` - Cerrar sesión

## 🎨 Personalización

### Cambiar tema de Fluent UI
Los componentes usan el tema por defecto de Fluent UI. Puedes personalizar colores, tipografía y espaciado modificando los estilos en los componentes.

### Agregar nuevos componentes
Sigue la estructura existente:
1. Crear el componente en `src/components/`
2. Definir tipos en `src/types/` si es necesario
3. Agregar servicios en `src/services/` para llamadas a API
4. Integrar en el contexto apropiado

## 🐛 Solución de Problemas

### Error de CORS
Si encuentras errores de CORS, asegúrate de que el backend tenga configurado CORS para permitir requests desde `http://localhost:3000`.

### Token expirado
Los tokens JWT tienen un tiempo de expiración. Si recibes errores 401, intenta hacer login nuevamente.

### Puerto ocupado
Si el puerto 3000 está ocupado, Rsbuild automáticamente usará el siguiente puerto disponible.

## 📚 Documentación Adicional

- [Implementación de Autenticación](docs/AUTHENTICATION_IMPLEMENTATION.md)
- [Documentación de la API](docs/Autentificacion.md)
- [Fluent UI Documentation](https://developer.microsoft.com/en-us/fluentui)
- [Rsbuild Documentation](https://rsbuild.dev/)

## 🤝 Contribución

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit tus cambios (`git commit -m 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la licencia MIT. Ver el archivo `LICENSE` para más detalles.
