import React, { useState } from 'react';
import {
  <PERSON>ack,
  TextField,
  PrimaryButton,
  Text,
  Icon,
  IStackTokens,
  IStackStyles
} from '@fluentui/react';
import { useAuth } from '../contexts/AuthContext';
import { LoginRequest } from '../types/auth';

const stackTokens: IStackTokens = { childrenGap: 20 };

const containerStyles: Partial<IStackStyles> = {
  root: {
    width: '400px',
    margin: '0 auto',
    padding: '40px',
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
  },
};

interface LoginFormProps {
  onSwitchToSignup: () => void;
}

export const LoginForm: React.FC<LoginFormProps> = ({ onSwitchToSignup }) => {
  console.log('🔧 LoginForm renderizando...');
  const { login } = useAuth();
  const [formData, setFormData] = useState<LoginRequest>({
    username: '',
    password: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('🔧 LoginForm - Submit con:', formData);
    await login(formData);
  };

  return (
    <Stack styles={containerStyles} tokens={stackTokens}>
      <Stack horizontal horizontalAlign="center" tokens={{ childrenGap: 10 }}>
        <Icon iconName="Contact" style={{ fontSize: '24px', color: '#0078d4' }} />
        <Text variant="xLarge" style={{ fontWeight: 'bold', color: '#323130' }}>
          🔧 Login Form (Simplificado)
        </Text>
      </Stack>

      <form onSubmit={handleSubmit}>
        <Stack tokens={stackTokens}>
          <TextField
            label="Nombre de usuario"
            placeholder="Ingresa tu nombre de usuario"
            value={formData.username}
            onChange={(_, value) => setFormData(prev => ({ ...prev, username: value || '' }))}
          />

          <TextField
            label="Contraseña"
            type="password"
            placeholder="Ingresa tu contraseña"
            value={formData.password}
            onChange={(_, value) => setFormData(prev => ({ ...prev, password: value || '' }))}
          />

          <PrimaryButton
            text="Iniciar Sesión"
            type="submit"
            iconProps={{ iconName: 'SignIn' }}
          />

          <PrimaryButton
            text="Cambiar a Registro"
            onClick={onSwitchToSignup}
            iconProps={{ iconName: 'AddFriend' }}
          />
        </Stack>
      </form>
    </Stack>
  );
};
