import React, { useState } from 'react';
import {
  Stack,
  TextField,
  PrimaryButton,
  Text,
  Icon,
  MessageBar,
  MessageBarType,
  IStackTokens,
  IStackStyles
} from '@fluentui/react';
import { useAuth } from '../contexts/AuthContext';
import { LoginRequest } from '../types/auth';

const stackTokens: IStackTokens = { childrenGap: 20 };

const containerStyles: Partial<IStackStyles> = {
  root: {
    width: '400px',
    margin: '0 auto',
    padding: '40px',
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
  },
};

interface LoginFormProps {
  onSwitchToSignup: () => void;
}

export const LoginForm: React.FC<LoginFormProps> = ({ onSwitchToSignup }) => {
  console.log('🔧 LoginForm renderizando...');
  const { login, isLoading, error } = useAuth();
  const [formData, setFormData] = useState<LoginRequest>({
    username: '',
    password: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('🔧 LoginForm - Submit con:', formData);

    if (!formData.username.trim() || !formData.password.trim()) {
      alert('Por favor completa todos los campos');
      return;
    }

    try {
      await login(formData);
    } catch (err) {
      // El error ya se maneja en el contexto
      console.log('🔧 Error capturado en LoginForm');
    }
  };

  return (
    <Stack styles={containerStyles} tokens={stackTokens}>
      <Stack horizontal horizontalAlign="center" tokens={{ childrenGap: 10 }}>
        <Icon iconName="Contact" style={{ fontSize: '24px', color: '#0078d4' }} />
        <Text variant="xLarge" style={{ fontWeight: 'bold', color: '#323130' }}>
          Iniciar Sesión
        </Text>
      </Stack>

      {error && (
        <MessageBar messageBarType={MessageBarType.error} isMultiline>
          {error}
        </MessageBar>
      )}

      <form onSubmit={handleSubmit}>
        <Stack tokens={stackTokens}>
          <TextField
            label="Nombre de usuario"
            placeholder="Ingresa tu nombre de usuario (prueba: admin)"
            value={formData.username}
            onChange={(_, value) => setFormData(prev => ({ ...prev, username: value || '' }))}
            disabled={isLoading}
          />

          <TextField
            label="Contraseña"
            type="password"
            placeholder="Ingresa tu contraseña (prueba: admin123)"
            value={formData.password}
            onChange={(_, value) => setFormData(prev => ({ ...prev, password: value || '' }))}
            disabled={isLoading}
          />

          <PrimaryButton
            text={isLoading ? 'Iniciando sesión...' : 'Iniciar Sesión'}
            type="submit"
            disabled={isLoading}
            iconProps={{ iconName: 'SignIn' }}
          />

          <Text variant="medium" style={{ textAlign: 'center' }}>
            ¿No tienes cuenta?{' '}
            <span
              style={{ color: '#0078d4', cursor: 'pointer', textDecoration: 'underline' }}
              onClick={onSwitchToSignup}
            >
              Regístrate aquí
            </span>
          </Text>
        </Stack>
      </form>
    </Stack>
  );
};
