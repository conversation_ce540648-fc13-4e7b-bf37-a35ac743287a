import React, { useState } from 'react';
import {
  Stack,
  TextField,
  PrimaryButton,
  Text,
  MessageBar,
  MessageBarType,
  Icon,
  IStackTokens,
  IStackStyles,
  ITextFieldStyles,
  IButtonStyles
} from '@fluentui/react';
import { useAuth } from '../contexts/AuthContext';
import { LoginRequest } from '../types/auth';

const stackTokens: IStackTokens = { childrenGap: 20 };

const containerStyles: Partial<IStackStyles> = {
  root: {
    width: '400px',
    margin: '0 auto',
    padding: '40px',
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
  },
};

const textFieldStyles: Partial<ITextFieldStyles> = {
  root: {
    width: '100%',
  },
};

const buttonStyles: Partial<IButtonStyles> = {
  root: {
    width: '100%',
    height: '40px',
  },
};

interface LoginFormProps {
  onSwitchToSignup: () => void;
}

export const LoginForm: React.FC<LoginFormProps> = ({ onSwitchToSignup }) => {
  const { login, isLoading, error } = useAuth();
  const [formData, setFormData] = useState<LoginRequest>({
    username: '',
    password: '',
  });
  const [validationErrors, setValidationErrors] = useState<Partial<LoginRequest>>({});

  const handleInputChange = (field: keyof LoginRequest, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Limpiar error de validación cuando el usuario empiece a escribir
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const errors: Partial<LoginRequest> = {};
    
    if (!formData.username.trim()) {
      errors.username = 'El nombre de usuario es requerido';
    }
    
    if (!formData.password.trim()) {
      errors.password = 'La contraseña es requerida';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await login(formData);
    } catch (err) {
      // El error ya se maneja en el contexto
    }
  };

  return (
    <Stack styles={containerStyles} tokens={stackTokens}>
      <Stack horizontal horizontalAlign="center" tokens={{ childrenGap: 10 }}>
        <Icon iconName="Contact" style={{ fontSize: '24px', color: '#0078d4' }} />
        <Text variant="xLarge" style={{ fontWeight: 'bold', color: '#323130' }}>
          Iniciar Sesión
        </Text>
      </Stack>

      {error && (
        <MessageBar messageBarType={MessageBarType.error} isMultiline>
          {error}
        </MessageBar>
      )}

      <form onSubmit={handleSubmit}>
        <Stack tokens={stackTokens}>
          <TextField
            label="Nombre de usuario"
            placeholder="Ingresa tu nombre de usuario"
            value={formData.username}
            onChange={(_, value) => handleInputChange('username', value || '')}
            errorMessage={validationErrors.username}
            styles={textFieldStyles}
            iconProps={{ iconName: 'Contact' }}
            required
          />

          <TextField
            label="Contraseña"
            type="password"
            placeholder="Ingresa tu contraseña"
            value={formData.password}
            onChange={(_, value) => handleInputChange('password', value || '')}
            errorMessage={validationErrors.password}
            styles={textFieldStyles}
            iconProps={{ iconName: 'Lock' }}
            required
          />

          <PrimaryButton
            text={isLoading ? 'Iniciando sesión...' : 'Iniciar Sesión'}
            type="submit"
            disabled={isLoading}
            styles={buttonStyles}
            iconProps={{ iconName: 'SignIn' }}
          />

          <Stack horizontal horizontalAlign="center" tokens={{ childrenGap: 5 }}>
            <Text variant="medium">¿No tienes cuenta?</Text>
            <Text
              variant="medium"
              style={{ color: '#0078d4', cursor: 'pointer', textDecoration: 'underline' }}
              onClick={onSwitchToSignup}
            >
              Regístrate aquí
            </Text>
          </Stack>
        </Stack>
      </form>
    </Stack>
  );
};
