import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  DefaultButton,
  PrimaryButton,
  Icon,
  Persona,
  PersonaSize,
  IStackTokens,
  IStackStyles,
  IButtonStyles,
  MessageBar,
  MessageBarType
} from '@fluentui/react';
import { useAuth } from '../contexts/AuthContext';
import { UserRole } from '../types/auth';

const stackTokens: IStackTokens = { childrenGap: 20 };

const containerStyles: Partial<IStackStyles> = {
  root: {
    width: '500px',
    margin: '0 auto',
    padding: '40px',
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
  },
};

const buttonStyles: Partial<IButtonStyles> = {
  root: {
    width: '200px',
  },
};

const getRoleDisplayName = (role: string): string => {
  switch (role) {
    case UserRole.ADMIN:
      return 'Administrador';
    case UserRole.VENDEDOR:
      return 'Vendedor';
    case UserRole.USUARIO:
      return 'Usuario';
    default:
      return role.replace('ROLE_', '');
  }
};

const getRoleIcon = (role: string): string => {
  switch (role) {
    case UserRole.ADMIN:
      return 'AdminALogoInverse32';
    case UserRole.VENDEDOR:
      return 'Shop';
    case UserRole.USUARIO:
      return 'Contact';
    default:
      return 'Contact';
  }
};

const getRoleColor = (role: string): string => {
  switch (role) {
    case UserRole.ADMIN:
      return '#d13438'; // Rojo
    case UserRole.VENDEDOR:
      return '#107c10'; // Verde
    case UserRole.USUARIO:
      return '#0078d4'; // Azul
    default:
      return '#605e5c'; // Gris
  }
};

export const UserProfile: React.FC = () => {
  const { user, logout, isLoading } = useAuth();

  if (!user) {
    return (
      <MessageBar messageBarType={MessageBarType.warning}>
        No hay información de usuario disponible.
      </MessageBar>
    );
  }

  const handleLogout = async () => {
    try {
      await logout();
    } catch (err) {
      console.error('Error al cerrar sesión:', err);
    }
  };

  const displayName = user.firstName && user.lastName 
    ? `${user.firstName} ${user.lastName}`
    : user.username;

  const initials = user.firstName && user.lastName
    ? `${user.firstName[0]}${user.lastName[0]}`
    : user.username.substring(0, 2).toUpperCase();

  return (
    <Stack styles={containerStyles} tokens={stackTokens}>
      <Stack horizontal horizontalAlign="center" tokens={{ childrenGap: 10 }}>
        <Icon iconName="ContactInfo" style={{ fontSize: '24px', color: '#0078d4' }} />
        <Text variant="xLarge" style={{ fontWeight: 'bold', color: '#323130' }}>
          Perfil de Usuario
        </Text>
      </Stack>

      <Stack horizontalAlign="center" tokens={{ childrenGap: 15 }}>
        <Persona
          text={displayName}
          secondaryText={user.email}
          size={PersonaSize.size72}
          imageInitials={initials}
        />
      </Stack>

      <Stack tokens={{ childrenGap: 15 }}>
        <Stack>
          <Text variant="mediumPlus" style={{ fontWeight: 'bold', marginBottom: '8px' }}>
            Información Personal
          </Text>
          
          <Stack tokens={{ childrenGap: 8 }}>
            <Stack horizontal tokens={{ childrenGap: 10 }}>
              <Icon iconName="Contact" style={{ fontSize: '16px', color: '#605e5c' }} />
              <Text variant="medium">
                <strong>Usuario:</strong> {user.username}
              </Text>
            </Stack>
            
            <Stack horizontal tokens={{ childrenGap: 10 }}>
              <Icon iconName="Mail" style={{ fontSize: '16px', color: '#605e5c' }} />
              <Text variant="medium">
                <strong>Email:</strong> {user.email}
              </Text>
            </Stack>
            
            <Stack horizontal tokens={{ childrenGap: 10 }}>
              <Icon iconName="NumberField" style={{ fontSize: '16px', color: '#605e5c' }} />
              <Text variant="medium">
                <strong>ID:</strong> {user.id}
              </Text>
            </Stack>
          </Stack>
        </Stack>

        <Stack>
          <Text variant="mediumPlus" style={{ fontWeight: 'bold', marginBottom: '8px' }}>
            Roles y Permisos
          </Text>
          
          <Stack tokens={{ childrenGap: 8 }}>
            {user.roles.map((role, index) => (
              <Stack key={index} horizontal tokens={{ childrenGap: 10 }}>
                <Icon 
                  iconName={getRoleIcon(role)} 
                  style={{ fontSize: '16px', color: getRoleColor(role) }} 
                />
                <Text 
                  variant="medium" 
                  style={{ color: getRoleColor(role), fontWeight: 'bold' }}
                >
                  {getRoleDisplayName(role)}
                </Text>
              </Stack>
            ))}
          </Stack>
        </Stack>

        <Stack horizontal horizontalAlign="center" tokens={{ childrenGap: 15 }}>
          <DefaultButton
            text="Editar Perfil"
            iconProps={{ iconName: 'Edit' }}
            styles={buttonStyles}
            onClick={() => {
              // TODO: Implementar edición de perfil
              alert('Funcionalidad de edición de perfil próximamente');
            }}
          />
          
          <PrimaryButton
            text={isLoading ? 'Cerrando sesión...' : 'Cerrar Sesión'}
            iconProps={{ iconName: 'SignOut' }}
            styles={buttonStyles}
            onClick={handleLogout}
            disabled={isLoading}
          />
        </Stack>
      </Stack>
    </Stack>
  );
};
