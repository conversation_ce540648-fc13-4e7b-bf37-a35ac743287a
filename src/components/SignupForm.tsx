import React, { useState } from 'react';
import {
  <PERSON>ack,
  TextField,
  PrimaryButton,
  Text,
  MessageBar,
  MessageBarType,
  Icon,
  Dropdown,
  IDropdownOption,
  IStackTokens,
  IStackStyles,
  ITextFieldStyles,
  IButtonStyles
} from '@fluentui/react';
import { useAuth } from '../contexts/AuthContext';
import { SignupRequest } from '../types/auth';

const stackTokens: IStackTokens = { childrenGap: 15 };

const containerStyles: Partial<IStackStyles> = {
  root: {
    width: '450px',
    margin: '0 auto',
    padding: '40px',
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
  },
};

const textFieldStyles: Partial<ITextFieldStyles> = {
  root: {
    width: '100%',
  },
};

const buttonStyles: Partial<IButtonStyles> = {
  root: {
    width: '100%',
    height: '40px',
  },
};

const roleOptions: IDropdownOption[] = [
  { key: 'usuario', text: 'Usuario' },
  { key: 'vendedor', text: 'Vendedor' },
  { key: 'admin', text: 'Administrador' },
];

interface SignupFormProps {
  onSwitchToLogin: () => void;
  onSignupSuccess: () => void;
}

export const SignupForm: React.FC<SignupFormProps> = ({ onSwitchToLogin, onSignupSuccess }) => {
  const { signup, isLoading, error } = useAuth();
  const [formData, setFormData] = useState<SignupRequest>({
    username: '',
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    roles: ['usuario'],
  });
  const [validationErrors, setValidationErrors] = useState<Partial<Record<keyof SignupRequest, string>>>({});
  const [successMessage, setSuccessMessage] = useState<string>('');

  const handleInputChange = (field: keyof SignupRequest, value: string | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Limpiar error de validación cuando el usuario empiece a escribir
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const errors: Partial<Record<keyof SignupRequest, string>> = {};
    
    if (!formData.username.trim()) {
      errors.username = 'El nombre de usuario es requerido';
    } else if (formData.username.length < 3 || formData.username.length > 20) {
      errors.username = 'El nombre de usuario debe tener entre 3 y 20 caracteres';
    }
    
    if (!formData.email.trim()) {
      errors.email = 'El email es requerido';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'El formato del email no es válido';
    } else if (formData.email.length > 50) {
      errors.email = 'El email no puede tener más de 50 caracteres';
    }
    
    if (!formData.password.trim()) {
      errors.password = 'La contraseña es requerida';
    } else if (formData.password.length < 6 || formData.password.length > 40) {
      errors.password = 'La contraseña debe tener entre 6 y 40 caracteres';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await signup(formData);
      setSuccessMessage('¡Usuario registrado exitosamente! Ahora puedes iniciar sesión.');
      setTimeout(() => {
        onSignupSuccess();
      }, 2000);
    } catch (err) {
      // El error ya se maneja en el contexto
    }
  };

  return (
    <Stack styles={containerStyles} tokens={stackTokens}>
      <Stack horizontal horizontalAlign="center" tokens={{ childrenGap: 10 }}>
        <Icon iconName="AddFriend" style={{ fontSize: '24px', color: '#0078d4' }} />
        <Text variant="xLarge" style={{ fontWeight: 'bold', color: '#323130' }}>
          Crear Cuenta
        </Text>
      </Stack>

      {error && (
        <MessageBar messageBarType={MessageBarType.error} isMultiline>
          {error}
        </MessageBar>
      )}

      {successMessage && (
        <MessageBar messageBarType={MessageBarType.success} isMultiline>
          {successMessage}
        </MessageBar>
      )}

      <form onSubmit={handleSubmit}>
        <Stack tokens={stackTokens}>
          <Stack horizontal tokens={{ childrenGap: 10 }}>
            <TextField
              label="Nombre"
              placeholder="Nombre (opcional)"
              value={formData.firstName || ''}
              onChange={(_, value) => handleInputChange('firstName', value || '')}
              styles={{ root: { flex: 1 } }}
              iconProps={{ iconName: 'Contact' }}
            />
            <TextField
              label="Apellido"
              placeholder="Apellido (opcional)"
              value={formData.lastName || ''}
              onChange={(_, value) => handleInputChange('lastName', value || '')}
              styles={{ root: { flex: 1 } }}
            />
          </Stack>

          <TextField
            label="Nombre de usuario *"
            placeholder="Ingresa tu nombre de usuario (3-20 caracteres)"
            value={formData.username}
            onChange={(_, value) => handleInputChange('username', value || '')}
            errorMessage={validationErrors.username}
            styles={textFieldStyles}
            iconProps={{ iconName: 'Contact' }}
            required
          />

          <TextField
            label="Email *"
            placeholder="Ingresa tu email"
            value={formData.email}
            onChange={(_, value) => handleInputChange('email', value || '')}
            errorMessage={validationErrors.email}
            styles={textFieldStyles}
            iconProps={{ iconName: 'Mail' }}
            required
          />

          <TextField
            label="Contraseña *"
            type="password"
            placeholder="Ingresa tu contraseña (6-40 caracteres)"
            value={formData.password}
            onChange={(_, value) => handleInputChange('password', value || '')}
            errorMessage={validationErrors.password}
            styles={textFieldStyles}
            iconProps={{ iconName: 'Lock' }}
            required
          />

          <Dropdown
            label="Rol"
            placeholder="Selecciona un rol"
            options={roleOptions}
            selectedKey={formData.roles?.[0] || 'usuario'}
            onChange={(_, option) => handleInputChange('roles', option ? [option.key as string] : ['usuario'])}
            styles={{ root: { width: '100%' } }}
          />

          <PrimaryButton
            text={isLoading ? 'Registrando...' : 'Crear Cuenta'}
            type="submit"
            disabled={isLoading}
            styles={buttonStyles}
            iconProps={{ iconName: 'AddFriend' }}
          />

          <Stack horizontal horizontalAlign="center" tokens={{ childrenGap: 5 }}>
            <Text variant="medium">¿Ya tienes cuenta?</Text>
            <Text
              variant="medium"
              style={{ color: '#0078d4', cursor: 'pointer', textDecoration: 'underline' }}
              onClick={onSwitchToLogin}
            >
              Inicia sesión aquí
            </Text>
          </Stack>
        </Stack>
      </form>
    </Stack>
  );
};
