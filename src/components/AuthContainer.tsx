import React, { useState } from 'react';
import {
  Stack,
  Text,
  DefaultButton,
  IStackTokens,
  IStackStyles
} from '@fluentui/react';
import { LoginForm } from './LoginForm';
import { SignupForm } from './SignupForm';
import { UserProfile } from './UserProfile';
import { useAuth } from '../contexts/AuthContext';

const stackTokens: IStackTokens = { childrenGap: 30 };

const containerStyles: Partial<IStackStyles> = {
  root: {
    width: '100%',
    minHeight: '100vh',
    backgroundColor: '#f3f2f1',
    padding: '40px 20px',
  },
};

const headerStyles: Partial<IStackStyles> = {
  root: {
    textAlign: 'center',
    marginBottom: '20px',
  },
};

type AuthView = 'login' | 'signup';

export const AuthContainer: React.FC = () => {
  const { user } = useAuth();
  const [currentView, setCurrentView] = useState<AuthView>('login');

  // Si el usuario está autenticado, mostrar el perfil
  if (user) {
    return (
      <Stack styles={containerStyles} tokens={stackTokens}>
        <Stack styles={headerStyles}>
          <Text variant="xxLarge" style={{ fontWeight: 'bold', color: '#323130' }}>
            Bienvenido, {user.username}!
          </Text>
          <Text variant="large" style={{ color: '#605e5c' }}>
            Sistema de Ecommerce - Panel de Usuario
          </Text>
        </Stack>
        <UserProfile />
      </Stack>
    );
  }

  // Si no está autenticado, mostrar formularios de login/signup
  return (
    <Stack styles={containerStyles} tokens={stackTokens}>
      <Stack styles={headerStyles}>
        <Text variant="xxLarge" style={{ fontWeight: 'bold', color: '#323130' }}>
          Sistema de Ecommerce
        </Text>
        <Text variant="large" style={{ color: '#605e5c' }}>
          Plataforma de Comercio Electrónico
        </Text>
      </Stack>

      {currentView === 'login' ? (
        <LoginForm onSwitchToSignup={() => setCurrentView('signup')} />
      ) : (
        <SignupForm 
          onSwitchToLogin={() => setCurrentView('login')}
          onSignupSuccess={() => setCurrentView('login')}
        />
      )}

      <Stack horizontal horizontalAlign="center" tokens={{ childrenGap: 15 }}>
        <DefaultButton
          text="Iniciar Sesión"
          onClick={() => setCurrentView('login')}
          primary={currentView === 'login'}
        />
        <DefaultButton
          text="Crear Cuenta"
          onClick={() => setCurrentView('signup')}
          primary={currentView === 'signup'}
        />
      </Stack>
    </Stack>
  );
};
