import React, { useState } from 'react';
import {
  Stack,
  Text,
  DefaultButton,
  IStackTokens,
  IStackStyles
} from '@fluentui/react';

const stackTokens: IStackTokens = { childrenGap: 30 };

const containerStyles: Partial<IStackStyles> = {
  root: {
    width: '100%',
    minHeight: '100vh',
    backgroundColor: '#f3f2f1',
    padding: '40px 20px',
  },
};

const headerStyles: Partial<IStackStyles> = {
  root: {
    textAlign: 'center',
    marginBottom: '20px',
  },
};

export const AuthContainer: React.FC = () => {
  console.log('AuthContainer renderizando...');

  return (
    <Stack styles={containerStyles} tokens={stackTokens}>
      <Stack styles={headerStyles}>
        <Text variant="xxLarge" style={{ fontWeight: 'bold', color: '#323130' }}>
          🔧 Debug: Sistema de Ecommerce
        </Text>
        <Text variant="large" style={{ color: '#605e5c' }}>
          AuthContainer está funcionando correctamente
        </Text>
      </Stack>

      <DefaultButton
        text="Botón de Prueba en AuthContainer"
        onClick={() => alert('AuthContainer funciona!')}
      />
    </Stack>
  );
};
