import React from 'react';
import { Stack, Text, PrimaryButton } from '@fluentui/react';
import { AuthProvider, useAuth } from './contexts/AuthContext';

const AppContent: React.FC = () => {
  const { user, login, logout } = useAuth();

  return (
    <Stack
      horizontalAlign="center"
      verticalAlign="center"
      styles={{
        root: {
          height: '100vh',
          backgroundColor: '#f3f2f1',
          padding: '20px'
        }
      }}
      tokens={{ childrenGap: 20 }}
    >
      <Text variant="xxLarge" style={{ color: '#0078d4', fontWeight: 'bold' }}>
        ✅ React + Fluent UI + AuthProvider
      </Text>
      <Text variant="large">
        Usuario actual: {user ? user.username : 'No autenticado'}
      </Text>
      <PrimaryButton
        text="Probar Login"
        onClick={() => login({ username: 'test', password: 'test' })}
        iconProps={{ iconName: 'Contact' }}
      />
      <PrimaryButton
        text="Probar Logout"
        onClick={() => logout()}
        iconProps={{ iconName: 'SignOut' }}
      />
    </Stack>
  );
};

export const App: React.FunctionComponent = () => {
  console.log('🔧 App.tsx con AuthProvider');
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
};

export default App;
