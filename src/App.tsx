import React from 'react';
import { Stack, Text, PrimaryButton } from '@fluentui/react';
import { AuthProvider, useAuth } from './contexts/AuthContext';

const AppContent: React.FC = () => {
  console.log('🔧 DIAGNÓSTICO: AppContent cargando...');
  const { user, isLoading, error } = useAuth();

  return (
    <div style={{
      backgroundColor: 'green',
      minHeight: '100vh',
      padding: '50px',
      color: 'white',
      fontSize: '20px',
      textAlign: 'center'
    }}>
      <h1>🔧 DIAGNÓSTICO: AuthContext</h1>
      <p>✅ React funciona</p>
      <p>✅ Fluent UI funciona</p>
      <p>✅ AuthContext cargado</p>

      <Stack
        horizontalAlign="center"
        tokens={{ childrenGap: 20 }}
        style={{ marginTop: '20px' }}
      >
        <Text variant="xxLarge" style={{ color: 'white' }}>
          Estado del Auth:
        </Text>
        <Text variant="large" style={{ color: 'white' }}>
          Usuario: {user ? user.username : 'No autenticado'}
        </Text>
        <Text variant="medium" style={{ color: 'white' }}>
          Cargando: {isLoading ? 'Sí' : 'No'}
        </Text>
        <Text variant="medium" style={{ color: 'white' }}>
          Error: {error || 'Ninguno'}
        </Text>
        <PrimaryButton
          text="✅ AuthContext Funciona"
          onClick={() => alert('¡AuthContext funciona perfectamente!')}
        />
      </Stack>

      <div style={{ marginTop: '30px', fontSize: '16px' }}>
        <p><strong>Próximo paso:</strong> Si ves esto, vamos a agregar LoginForm</p>
      </div>
    </div>
  );
};

export const App: React.FunctionComponent = () => {
  console.log('🔧 DIAGNÓSTICO: App.tsx con AuthProvider...');

  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
};

export default App;
