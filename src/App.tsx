import React from 'react';
import { Stack, Text, PrimaryButton } from '@fluentui/react';

export const App: React.FunctionComponent = () => {
  console.log('🔧 App.tsx - versión de emergencia');
  return (
    <Stack
      horizontalAlign="center"
      verticalAlign="center"
      styles={{
        root: {
          height: '100vh',
          backgroundColor: '#f3f2f1',
          padding: '20px'
        }
      }}
      tokens={{ childrenGap: 20 }}
    >
      <Text variant="xxLarge" style={{ color: '#d13438', fontWeight: 'bold' }}>
        🚨 Modo de Emergencia
      </Text>
      <Text variant="large">
        Algo se rompió en el código. Vamos a arreglarlo paso a paso.
      </Text>
      <PrimaryButton
        text="Aplicación Funcionando"
        onClick={() => alert('React y Fluent UI están funcionando')}
        iconProps={{ iconName: 'CheckMark' }}
      />
    </Stack>
  );
};

export default App;
