import React, { useState } from 'react';
import { Stack, Text } from '@fluentui/react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { LoginForm } from './components/LoginForm';

const AppContent: React.FC = () => {
  const { user } = useAuth();
  const [showLogin, setShowLogin] = useState(true);

  if (user) {
    return (
      <Stack
        horizontalAlign="center"
        verticalAlign="center"
        styles={{
          root: {
            height: '100vh',
            backgroundColor: '#f3f2f1',
            padding: '20px'
          }
        }}
        tokens={{ childrenGap: 20 }}
      >
        <Text variant="xxLarge" style={{ color: '#0078d4', fontWeight: 'bold' }}>
          ✅ ¡Bienvenido {user.username}!
        </Text>
        <Text variant="large">
          Login funcionando correctamente
        </Text>
      </Stack>
    );
  }

  return (
    <Stack
      styles={{
        root: {
          minHeight: '100vh',
          backgroundColor: '#f3f2f1',
          padding: '20px'
        }
      }}
      tokens={{ childrenGap: 20 }}
    >
      <Text variant="xxLarge" style={{ color: '#0078d4', fontWeight: 'bold', textAlign: 'center' }}>
        🔧 Probando LoginForm
      </Text>
      {showLogin && (
        <LoginForm onSwitchToSignup={() => setShowLogin(false)} />
      )}
    </Stack>
  );
};

export const App: React.FunctionComponent = () => {
  console.log('🔧 App.tsx con AuthProvider');
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
};

export default App;
