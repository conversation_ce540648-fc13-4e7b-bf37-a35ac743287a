import React, { useState } from 'react';
import { Stack, Text, PrimaryButton, DefaultButton } from '@fluentui/react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { LoginForm } from './components/LoginForm';
import { SignupForm } from './components/SignupForm';

type AuthView = 'login' | 'signup';

const AppContent: React.FC = () => {
  const { user, logout, isLoading } = useAuth();
  const [currentView, setCurrentView] = useState<AuthView>('login');

  if (user) {
    return (
      <Stack
        horizontalAlign="center"
        verticalAlign="center"
        styles={{
          root: {
            height: '100vh',
            backgroundColor: '#f3f2f1',
            padding: '20px'
          }
        }}
        tokens={{ childrenGap: 20 }}
      >
        <Text variant="xxLarge" style={{ color: '#107c10', fontWeight: 'bold' }}>
          🎉 ¡Bienvenido {user.username}!
        </Text>
        <Text variant="large">
          📧 Email: {user.email}
        </Text>
        <Text variant="medium">
          🔐 Roles: {user.roles.join(', ')}
        </Text>
        <Text variant="large" style={{ color: '#0078d4' }}>
          ✅ Conectado a la API Real - Los datos se guardan en la BD
        </Text>
        <PrimaryButton
          text={isLoading ? 'Cerrando sesión...' : 'Cerrar Sesión'}
          onClick={logout}
          disabled={isLoading}
          iconProps={{ iconName: 'SignOut' }}
        />
      </Stack>
    );
  }

  return (
    <Stack
      styles={{
        root: {
          minHeight: '100vh',
          backgroundColor: '#f3f2f1',
          padding: '20px'
        }
      }}
      tokens={{ childrenGap: 20 }}
    >
      <Text variant="xxLarge" style={{ color: '#0078d4', fontWeight: 'bold', textAlign: 'center' }}>
        🌐 Sistema de Ecommerce (API Real)
      </Text>

      {currentView === 'login' ? (
        <>
          <Text variant="large" style={{ textAlign: 'center', color: '#605e5c' }}>
            Credenciales de prueba: admin/admin123 o user/123456
          </Text>
          <LoginForm onSwitchToSignup={() => setCurrentView('signup')} />
        </>
      ) : (
        <>
          <Text variant="large" style={{ textAlign: 'center', color: '#605e5c' }}>
            Crea tu nueva cuenta - Se guardará en la base de datos real
          </Text>
          <SignupForm
            onSwitchToLogin={() => setCurrentView('login')}
            onSignupSuccess={() => setCurrentView('login')}
          />
        </>
      )}

      <Stack horizontal horizontalAlign="center" tokens={{ childrenGap: 15 }}>
        <DefaultButton
          text="Iniciar Sesión"
          onClick={() => setCurrentView('login')}
          primary={currentView === 'login'}
          iconProps={{ iconName: 'Contact' }}
        />
        <DefaultButton
          text="Crear Cuenta"
          onClick={() => setCurrentView('signup')}
          primary={currentView === 'signup'}
          iconProps={{ iconName: 'AddFriend' }}
        />
      </Stack>
    </Stack>
  );
};

export const App: React.FunctionComponent = () => {
  console.log('🔧 App.tsx con AuthProvider OFFLINE');
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
};

export default App;
