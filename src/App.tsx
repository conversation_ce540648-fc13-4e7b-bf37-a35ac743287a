import React from 'react';
import { Stack, Text, PrimaryButton } from '@fluentui/react';

export const App: React.FunctionComponent = () => {
  console.log('🔧 DIAGNÓSTICO: App.tsx cargando...');

  return (
    <div style={{
      backgroundColor: 'orange',
      minHeight: '100vh',
      padding: '50px',
      color: 'white',
      fontSize: '20px',
      textAlign: 'center'
    }}>
      <h1>🔧 DIAGNÓSTICO ACTIVO</h1>
      <p>Si ves esto, React está funcionando</p>
      <p>Ahora vamos a probar Fluent UI:</p>

      <Stack
        horizontalAlign="center"
        tokens={{ childrenGap: 20 }}
        style={{ marginTop: '20px' }}
      >
        <Text variant="xxLarge" style={{ color: 'white' }}>
          ✅ Fluent UI Text Component
        </Text>
        <PrimaryButton
          text="✅ Fluent UI Button"
          onClick={() => alert('¡Fluent UI funciona!')}
        />
      </Stack>

      <div style={{ marginTop: '30px', fontSize: '16px' }}>
        <p><strong>Próximo paso:</strong> Si ves esto, vamos a agregar AuthContext paso a paso</p>
      </div>
    </div>
  );
};

export default App;
