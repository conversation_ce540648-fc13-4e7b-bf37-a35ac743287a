import React, { useState } from 'react';
import { Stack, Text, PrimaryButton } from '@fluentui/react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { LoginForm } from './components/LoginForm';

const AppContent: React.FC = () => {
  const { user, logout, isLoading } = useAuth();
  const [showLogin, setShowLogin] = useState(true);

  if (user) {
    return (
      <Stack
        horizontalAlign="center"
        verticalAlign="center"
        styles={{
          root: {
            height: '100vh',
            backgroundColor: '#f3f2f1',
            padding: '20px'
          }
        }}
        tokens={{ childrenGap: 20 }}
      >
        <Text variant="xxLarge" style={{ color: '#0078d4', fontWeight: 'bold' }}>
          ✅ ¡Bienvenido {user.username}!
        </Text>
        <Text variant="large">
          Email: {user.email}
        </Text>
        <Text variant="medium">
          Roles: {user.roles.join(', ')}
        </Text>
        <Text variant="large" style={{ color: '#107c10' }}>
          🎉 ¡Autenticación funcionando perfectamente!
        </Text>
        <PrimaryButton
          text={isLoading ? 'Cerrando sesión...' : 'Cerrar Sesión'}
          onClick={logout}
          disabled={isLoading}
          iconProps={{ iconName: 'SignOut' }}
        />
      </Stack>
    );
  }

  return (
    <Stack
      styles={{
        root: {
          minHeight: '100vh',
          backgroundColor: '#f3f2f1',
          padding: '20px'
        }
      }}
      tokens={{ childrenGap: 20 }}
    >
      <Text variant="xxLarge" style={{ color: '#0078d4', fontWeight: 'bold', textAlign: 'center' }}>
        🔧 Probando LoginForm
      </Text>
      {showLogin && (
        <LoginForm onSwitchToSignup={() => setShowLogin(false)} />
      )}
    </Stack>
  );
};

export const App: React.FunctionComponent = () => {
  console.log('🔧 App.tsx con AuthProvider');
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
};

export default App;
