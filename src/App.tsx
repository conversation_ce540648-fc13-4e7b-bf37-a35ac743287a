import React from 'react';
import { Stack, Text, PrimaryButton } from '@fluentui/react';

export const App: React.FunctionComponent = () => {
  console.log('🔧 App.tsx - modo emergencia para diagnosticar API');
  return (
    <Stack
      horizontalAlign="center"
      verticalAlign="center"
      styles={{
        root: {
          height: '100vh',
          backgroundColor: '#f3f2f1',
          padding: '20px'
        }
      }}
      tokens={{ childrenGap: 20 }}
    >
      <Text variant="xxLarge" style={{ color: '#d13438', fontWeight: 'bold' }}>
        🚨 Diagnóstico de API
      </Text>
      <Text variant="large">
        Hay un problema al conectar con la API. Vamos a solucionarlo.
      </Text>
      <Text variant="medium" style={{ textAlign: 'center' }}>
        ¿Tu API está corriendo en http://localhost:8080?<br/>
        ¿Tiene configurado CORS para permitir http://localhost:3001?
      </Text>
      <PrimaryButton
        text="Probar Conexión a API"
        onClick={async () => {
          try {
            const response = await fetch('http://localhost:8080/api/auth/session-info');
            if (response.ok) {
              alert('✅ API conectada correctamente');
            } else {
              alert(`❌ API responde pero con error: ${response.status}`);
            }
          } catch (err) {
            alert(`❌ No se puede conectar a la API: ${err}`);
          }
        }}
        iconProps={{ iconName: 'PlugConnected' }}
      />
    </Stack>
  );
};

export default App;
