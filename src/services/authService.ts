import { LoginRequest, SignupRequest, JwtResponse, MessageResponse } from '../types/auth';
import { API_CONFIG, buildApiUrl } from '../config/api';

class AuthService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  async login(credentials: LoginRequest): Promise<JwtResponse> {
    const response = await fetch(buildApiUrl(API_CONFIG.AUTH_ENDPOINTS.LOGIN), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('Credenciales incorrectas');
      }
      throw new Error('Error en el servidor');
    }

    const data: JwtResponse = await response.json();
    
    // Guardar token en localStorage
    localStorage.setItem('token', data.token);
    localStorage.setItem('user', JSON.stringify({
      id: data.id,
      username: data.username,
      email: data.email,
      roles: data.roles
    }));

    return data;
  }

  async signup(userData: SignupRequest): Promise<MessageResponse> {
    const response = await fetch(buildApiUrl(API_CONFIG.AUTH_ENDPOINTS.SIGNUP), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      if (response.status === 400) {
        throw new Error(errorData?.message || 'Datos de registro inválidos');
      }
      throw new Error('Error en el servidor');
    }

    return await response.json();
  }

  async getSessionInfo(): Promise<JwtResponse | MessageResponse> {
    const response = await fetch(buildApiUrl(API_CONFIG.AUTH_ENDPOINTS.SESSION_INFO), {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error('Error al obtener información de sesión');
    }

    return await response.json();
  }

  async logout(): Promise<MessageResponse> {
    try {
      const response = await fetch(buildApiUrl(API_CONFIG.AUTH_ENDPOINTS.LOGOUT), {
        method: 'POST',
        headers: this.getAuthHeaders(),
      });

      if (!response.ok) {
        console.warn('Error al cerrar sesión en el servidor');
      }

      return await response.json();
    } finally {
      // Limpiar localStorage independientemente del resultado
      localStorage.removeItem('token');
      localStorage.removeItem('user');
    }
  }

  // Métodos utilitarios
  getStoredToken(): string | null {
    return localStorage.getItem('token');
  }

  getStoredUser(): any | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  isAuthenticated(): boolean {
    return !!this.getStoredToken();
  }
}

export const authService = new AuthService();
