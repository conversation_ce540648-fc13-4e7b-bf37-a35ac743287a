import React, { createContext, useContext, useState, ReactNode } from 'react';
import { AuthContextType, User, LoginRequest, SignupRequest } from '../types/auth';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  console.log('🔧 AuthProvider renderizando...');
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Funciones simplificadas para debug
  const login = async (credentials: LoginRequest): Promise<void> => {
    console.log('🔧 Login llamado con:', credentials);
    // TODO: Implementar login real
  };

  const signup = async (userData: SignupRequest): Promise<void> => {
    console.log('🔧 Signup llamado con:', userData);
    // TODO: Implementar signup real
  };

  const logout = async (): Promise<void> => {
    console.log('🔧 Logout llamado');
    setUser(null);
    setToken(null);
  };

  const value: AuthContextType = {
    user,
    token,
    login,
    signup,
    logout,
    isLoading,
    error
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth debe ser usado dentro de un AuthProvider');
  }
  return context;
};
