import React, { createContext, useContext, useState, ReactNode } from 'react';
import { AuthContextType, User, LoginRequest, SignupRequest } from '../types/auth';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  console.log('🔧 AuthProvider OFFLINE renderizando...');
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const login = async (credentials: LoginRequest): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🔧 Login OFFLINE con:', credentials);

      // Simular delay de red
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Simular validación offline
      if (credentials.username === 'admin' && credentials.password === 'admin123') {
        const mockUser: User = {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          roles: ['ROLE_ADMIN']
        };

        setToken('mock-jwt-token-123');
        setUser(mockUser);
        console.log('🔧 Login OFFLINE exitoso:', mockUser);
      } else if (credentials.username === 'user' && credentials.password === '123456') {
        const mockUser: User = {
          id: 2,
          username: 'user',
          email: '<EMAIL>',
          roles: ['ROLE_USUARIO']
        };

        setToken('mock-jwt-token-456');
        setUser(mockUser);
        console.log('🔧 Login OFFLINE exitoso:', mockUser);
      } else {
        throw new Error('Credenciales incorrectas. Prueba: admin/admin123 o user/123456');
      }
    } catch (err) {
      console.error('🔧 Error en login OFFLINE:', err);
      setError(err instanceof Error ? err.message : 'Error desconocido');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const signup = async (userData: SignupRequest): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🔧 Signup OFFLINE con:', userData);
      // Simular delay
      await new Promise(resolve => setTimeout(resolve, 800));
      console.log('🔧 Signup OFFLINE exitoso');
    } catch (err) {
      console.error('🔧 Error en signup OFFLINE:', err);
      setError(err instanceof Error ? err.message : 'Error desconocido');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🔧 Logout OFFLINE');
      // Simular delay
      await new Promise(resolve => setTimeout(resolve, 500));
      console.log('🔧 Logout OFFLINE exitoso');
    } catch (err) {
      console.warn('🔧 Error al cerrar sesión OFFLINE:', err);
    } finally {
      setToken(null);
      setUser(null);
      setIsLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    token,
    login,
    signup,
    logout,
    isLoading,
    error
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth debe ser usado dentro de un AuthProvider');
  }
  return context;
};
