import React, { createContext, useContext, useState, ReactNode } from 'react';
import { AuthContextType, User, LoginRequest, SignupRequest } from '../types/auth';
import { authService } from '../services/authService';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  console.log('🔧 AuthProvider renderizando...');
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const login = async (credentials: LoginRequest): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🔧 Intentando login con:', credentials);
      const response = await authService.login(credentials);
      console.log('🔧 Login exitoso:', response);

      setToken(response.token);
      setUser({
        id: response.id,
        username: response.username,
        email: response.email,
        roles: response.roles
      });
    } catch (err) {
      console.error('🔧 Error en login:', err);
      setError(err instanceof Error ? err.message : 'Error desconocido');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const signup = async (userData: SignupRequest): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🔧 Intentando signup con:', userData);
      await authService.signup(userData);
      console.log('🔧 Signup exitoso');
    } catch (err) {
      console.error('🔧 Error en signup:', err);
      setError(err instanceof Error ? err.message : 'Error desconocido');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🔧 Intentando logout');
      await authService.logout();
      console.log('🔧 Logout exitoso');
    } catch (err) {
      console.warn('🔧 Error al cerrar sesión:', err);
    } finally {
      setToken(null);
      setUser(null);
      setIsLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    token,
    login,
    signup,
    logout,
    isLoading,
    error
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth debe ser usado dentro de un AuthProvider');
  }
  return context;
};
